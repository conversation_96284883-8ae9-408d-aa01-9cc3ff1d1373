#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐平台大数据分析项目
Music Platform Big Data Analysis

基于Apache Spark的音乐平台用户行为数据分析
数据流程: CSV → MySQL → Spark读取MySQL → 分析处理
"""

import os
import warnings
from datetime import datetime
from typing import Dict, Any, Tuple

# Spark相关导入
from pyspark.sql import SparkSession
from pyspark.conf import SparkConf
from pyspark.sql.types import *
from pyspark.sql.functions import *
from pyspark.streaming import StreamingContext

# 数据处理和可视化
import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go

warnings.filterwarnings('ignore')

# 设置中文字体
font_path = '/root/job20/SimHei.ttf'
try:
    font_prop = fm.FontProperties(fname=font_path)
    fm.fontManager.addfont(font_path)
    plt.rcParams['font.family'] = [font_prop.get_name()]
    plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
    plt.rcParams['axes.unicode_minus'] = False
    print(f"✅ SimHei字体加载成功")
except Exception as e:
    print(f"⚠️ 字体加载失败: {e}")
    font_prop = fm.FontProperties()

# RDD操作函数
def analyze_user_behavior(user_id: str, actions: list) -> tuple:
    """分析用户行为模式"""
    if not actions:
        return None
    
    total_actions = len(actions)
    play_count = 0
    download_count = 0
    favorite_count = 0
    songs = set()
    dates = set()
    
    for action in actions:
        action_type, song_id, date = action[0], action[1], action[2]
        if action_type == 1:
            play_count += 1
        elif action_type == 2:
            download_count += 1
        elif action_type == 3:
            favorite_count += 1
        
        if song_id:
            songs.add(song_id)
        if date:
            dates.add(str(date))
    
    unique_songs = len(songs)
    unique_dates = len(dates) if dates else 1
    avg_daily_actions = float(total_actions) / unique_dates
    
    return (user_id, total_actions, play_count, download_count, 
           favorite_count, unique_songs, unique_dates, avg_daily_actions)

def calculate_song_popularity(song_data: tuple) -> tuple:
    """计算歌曲热度分数"""
    try:
        if not song_data or len(song_data) < 2:
            return None

        song_id = song_data[0]
        joined_data = song_data[1]

        # 安全地提取数据，处理leftOuterJoin的嵌套结构
        play_count = 0
        download_count = 0
        favorite_count = 0
        artist_id = None
        init_plays = 0
        language = None

        # leftOuterJoin的结果结构: ((play_count, download_count), favorite_count), song_info)
        if isinstance(joined_data, tuple) and len(joined_data) >= 2:
            # 第一层: ((play_count, download_count), favorite_count)
            first_join = joined_data[0]
            if isinstance(first_join, tuple) and len(first_join) >= 2:
                # 第二层: (play_count, download_count)
                play_download = first_join[0]
                if isinstance(play_download, tuple) and len(play_download) >= 2:
                    play_count = play_download[0] if play_download[0] else 0
                    download_count = play_download[1] if play_download[1] else 0

                # 收藏次数
                favorite_count = first_join[1] if first_join[1] else 0

            # 歌曲信息
            if len(joined_data) > 1 and joined_data[1]:
                song_info = joined_data[1]
                if isinstance(song_info, tuple) and len(song_info) >= 4:
                    artist_id = song_info[0]
                    init_plays = song_info[1] if song_info[1] else 0
                    language = song_info[2]

        # 计算热度分数
        popularity_score = float(play_count) + float(download_count) * 2 + \
                         float(favorite_count) * 3 + float(init_plays) * 0.1

        return (song_id, artist_id, int(play_count), int(download_count),
               int(favorite_count), int(init_plays), language, float(popularity_score))

    except Exception as e:
        return None

class MusicAnalyzer:
    """音乐平台数据分析器"""
    
    def __init__(self):
        self.spark = None
        self.sc = None
        self.data_path = "/root/job20/static/"
        self.output_path = "/root/job20/output/"
        self.charts_path = "/root/job20/charts/"
        self.font_prop = font_prop
        
        # 创建输出目录
        os.makedirs(self.output_path, exist_ok=True)
        os.makedirs(self.charts_path, exist_ok=True)
    
    def initialize_spark(self):
        """初始化Spark"""
        conf = SparkConf().setAppName("MusicAnalysis") \
            .set("spark.driver.memory", "4g") \
            .set("spark.executor.memory", "4g") \
            .set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
        
        self.spark = SparkSession.builder.config(conf=conf).getOrCreate()
        self.sc = self.spark.sparkContext
        self.sc.setLogLevel("WARN")
        
        print(f"✅ Spark初始化成功")
    
    def load_and_clean_data(self):
        """数据加载和清洗"""
        print("\n🧹 数据清洗...")
        
        # 加载用户行为数据
        user_actions = self.spark.read.csv(
            f"file://{self.data_path}mars_tianchi_user_actions.csv",
            header=False,
            schema=StructType([
                StructField("user_id", StringType(), True),
                StructField("song_id", StringType(), True),
                StructField("gmt_create", StringType(), True),
                StructField("action_type", StringType(), True),
                StructField("ds", StringType(), True)
            ])
        )
        
        # 加载歌曲数据
        songs = self.spark.read.csv(
            f"file://{self.data_path}mars_tianchi_songs.csv",
            header=False,
            schema=StructType([
                StructField("song_id", StringType(), True),
                StructField("artist_id", StringType(), True),
                StructField("publish_time", StringType(), True),
                StructField("song_init_plays", StringType(), True),
                StructField("language", StringType(), True),
                StructField("gender", StringType(), True)
            ])
        )
        
        # 数据清洗
        user_actions_clean = user_actions \
            .filter(col("user_id").isNotNull()) \
            .filter(col("song_id").isNotNull()) \
            .filter(col("action_type").isin(["1", "2", "3"])) \
            .withColumn("action_type_int", col("action_type").cast("int")) \
            .withColumn("action_date", to_date(from_unixtime(col("gmt_create").cast("long")))) \
            .withColumn("action_hour", hour(from_unixtime(col("gmt_create").cast("long"))))
        
        songs_clean = songs \
            .filter(col("song_id").isNotNull()) \
            .filter(col("artist_id").isNotNull()) \
            .withColumn("song_init_plays_int", col("song_init_plays").cast("long")) \
            .withColumn("language_int", col("language").cast("int")) \
            .withColumn("gender_int", col("gender").cast("int"))
        
        user_actions_clean.cache()
        songs_clean.cache()
        
        print(f"✅ 用户行为数据: {user_actions_clean.count():,} 条")
        print(f"✅ 歌曲数据: {songs_clean.count():,} 条")
        
        return user_actions_clean, songs_clean
    
    def rdd_operations(self, user_actions, songs):
        """RDD操作 - 2个复杂计算"""
        print("\n⚡ RDD操作...")
        
        # RDD操作1: 用户行为分析
        actions_rdd = user_actions.rdd.map(lambda row: (
            row.user_id, (row.action_type_int, row.song_id, row.action_date)
        ))
        
        user_stats = actions_rdd.groupByKey() \
            .map(lambda x: analyze_user_behavior(x[0], list(x[1]))) \
            .filter(lambda x: x is not None)
        
        user_stats_list = user_stats.collect()
        print(f"✅ 用户行为分析: {len(user_stats_list)} 个用户")
        
        # RDD操作2: 歌曲热度排行
        song_play_rdd = user_actions.filter(col("action_type_int") == 1) \
            .rdd.map(lambda row: (row.song_id, 1)).reduceByKey(lambda a, b: a + b)
        
        song_download_rdd = user_actions.filter(col("action_type_int") == 2) \
            .rdd.map(lambda row: (row.song_id, 1)).reduceByKey(lambda a, b: a + b)
        
        song_favorite_rdd = user_actions.filter(col("action_type_int") == 3) \
            .rdd.map(lambda row: (row.song_id, 1)).reduceByKey(lambda a, b: a + b)
        
        songs_rdd = songs.rdd.map(lambda row: (
            row.song_id, (row.artist_id, row.song_init_plays_int, row.language_int, row.gender_int)
        ))
        
        song_popularity = song_play_rdd.leftOuterJoin(song_download_rdd) \
            .leftOuterJoin(song_favorite_rdd).leftOuterJoin(songs_rdd) \
            .map(calculate_song_popularity).filter(lambda x: x is not None) \
            .sortBy(lambda x: x[7], ascending=False)
        
        top_songs = song_popularity.take(100)
        print(f"✅ 歌曲热度排行: Top {len(top_songs)} 首歌曲")
        
        return user_stats_list, top_songs

    def sql_queries(self, user_actions, songs):
        """SQL查询 - 3个复杂查询"""
        print("\n📊 SQL查询...")

        # 注册临时视图
        user_actions.createOrReplaceTempView("user_actions")
        songs.createOrReplaceTempView("songs")

        # SQL查询1: 多表连接 - 用户行为与歌曲信息关联分析
        query1 = """
        WITH user_song_stats AS (
            SELECT
                ua.user_id,
                ua.song_id,
                s.artist_id,
                COUNT(*) as total_actions,
                SUM(CASE WHEN ua.action_type_int = 1 THEN 1 ELSE 0 END) as plays,
                SUM(CASE WHEN ua.action_type_int = 2 THEN 1 ELSE 0 END) as downloads,
                SUM(CASE WHEN ua.action_type_int = 3 THEN 1 ELSE 0 END) as favorites
            FROM user_actions ua
            LEFT JOIN songs s ON ua.song_id = s.song_id
            GROUP BY ua.user_id, ua.song_id, s.artist_id
        )
        SELECT * FROM user_song_stats
        ORDER BY total_actions DESC
        LIMIT 1000
        """

        result1 = self.spark.sql(query1)
        print(f"✅ 查询1 - 用户歌曲关联: {result1.count()} 条记录")

        # SQL查询2: 窗口函数 - 用户行为趋势分析
        query2 = """
        WITH daily_stats AS (
            SELECT
                action_date,
                action_type_int,
                COUNT(*) as daily_count,
                COUNT(DISTINCT user_id) as daily_users
            FROM user_actions
            WHERE action_date IS NOT NULL
            GROUP BY action_date, action_type_int
        ),
        trend_analysis AS (
            SELECT
                action_date,
                action_type_int,
                daily_count,
                daily_users,
                LAG(daily_count, 1) OVER (PARTITION BY action_type_int ORDER BY action_date) as prev_count,
                AVG(daily_count) OVER (PARTITION BY action_type_int ORDER BY action_date
                                     ROWS BETWEEN 6 PRECEDING AND CURRENT ROW) as moving_avg
            FROM daily_stats
        )
        SELECT
            action_date,
            action_type_int,
            daily_count,
            daily_users,
            CASE
                WHEN prev_count IS NULL THEN 0
                ELSE ROUND((daily_count - prev_count) * 100.0 / prev_count, 2)
            END as growth_rate,
            ROUND(moving_avg, 2) as moving_avg_7d
        FROM trend_analysis
        ORDER BY action_date, action_type_int
        """

        result2 = self.spark.sql(query2)
        print(f"✅ 查询2 - 行为趋势分析: {result2.count()} 条记录")

        # SQL查询3: 复杂聚合 - 艺人热度排行
        query3 = """
        WITH artist_stats AS (
            SELECT
                s.artist_id,
                COUNT(DISTINCT ua.user_id) as unique_users,
                COUNT(DISTINCT ua.song_id) as unique_songs,
                SUM(CASE WHEN ua.action_type_int = 1 THEN 1 ELSE 0 END) as total_plays,
                SUM(CASE WHEN ua.action_type_int = 2 THEN 1 ELSE 0 END) as total_downloads,
                SUM(CASE WHEN ua.action_type_int = 3 THEN 1 ELSE 0 END) as total_favorites,
                AVG(s.song_init_plays_int) as avg_init_plays
            FROM user_actions ua
            JOIN songs s ON ua.song_id = s.song_id
            GROUP BY s.artist_id
        ),
        artist_ranking AS (
            SELECT
                artist_id,
                unique_users,
                unique_songs,
                total_plays,
                total_downloads,
                total_favorites,
                ROUND(avg_init_plays, 2) as avg_init_plays,
                (total_plays + total_downloads * 2 + total_favorites * 3) as popularity_score,
                ROW_NUMBER() OVER (ORDER BY (total_plays + total_downloads * 2 + total_favorites * 3) DESC) as rank
            FROM artist_stats
        )
        SELECT * FROM artist_ranking
        WHERE rank <= 50
        ORDER BY rank
        """

        result3 = self.spark.sql(query3)
        print(f"✅ 查询3 - 艺人热度排行: {result3.count()} 条记录")

        return result1, result2, result3

    def streaming_analysis(self):
        """实时流处理 - 2个实时场景"""
        print("\n🌊 实时流处理...")

        # 创建流处理上下文
        ssc = StreamingContext(self.sc, 10)  # 10秒批次间隔

        # 实时场景1: 实时用户行为统计
        print("📈 场景1: 实时用户行为统计")

        # 模拟实时数据流
        lines = ssc.textFileStream("/tmp/streaming_data")

        # 解析数据并统计
        user_actions = lines.map(lambda line: line.split(',')) \
            .filter(lambda parts: len(parts) >= 4) \
            .map(lambda parts: (parts[3], 1))  # (action_type, 1)

        action_counts = user_actions.reduceByKey(lambda a, b: a + b)
        action_counts.pprint()

        # 实时场景2: 异常行为检测
        print("🚨 场景2: 异常行为检测")

        # 检测高频用户
        user_frequency = lines.map(lambda line: line.split(',')) \
            .filter(lambda parts: len(parts) >= 4) \
            .map(lambda parts: (parts[0], 1)) \
            .reduceByKey(lambda a, b: a + b) \
            .filter(lambda x: x[1] > 100)  # 超过100次行为的用户

        user_frequency.pprint()

        print("✅ 流处理配置完成")
        return ssc

    def create_visualizations(self, user_stats, top_songs, sql_results):
        """创建可视化图表 - 每个图表单独保存"""
        print("\n📊 创建可视化图表...")

        # 转换数据为pandas DataFrame，处理空数据情况
        if len(user_stats) > 0:
            user_df = pd.DataFrame(user_stats, columns=[
                'user_id', 'total_actions', 'play_count', 'download_count',
                'favorite_count', 'unique_songs', 'active_days', 'avg_daily_actions'
            ])
        else:
            # 创建示例数据
            user_df = pd.DataFrame({
                'user_id': [f'用户{i}' for i in range(1, 101)],
                'total_actions': np.random.randint(1, 1000, 100),
                'play_count': np.random.randint(1, 800, 100),
                'download_count': np.random.randint(0, 100, 100),
                'favorite_count': np.random.randint(0, 50, 100),
                'unique_songs': np.random.randint(1, 100, 100),
                'active_days': np.random.randint(1, 30, 100),
                'avg_daily_actions': np.random.uniform(1, 50, 100)
            })
            print("⚠️ 使用示例数据生成图表")

        if len(top_songs) > 0:
            songs_df = pd.DataFrame(top_songs, columns=[
                'song_id', 'artist_id', 'play_count', 'download_count',
                'favorite_count', 'init_plays', 'language', 'popularity_score'
            ])
        else:
            # 创建示例数据
            songs_df = pd.DataFrame({
                'song_id': [f'歌曲{i}' for i in range(1, 101)],
                'artist_id': [f'艺人{i%20}' for i in range(1, 101)],
                'play_count': np.random.randint(100, 10000, 100),
                'download_count': np.random.randint(10, 1000, 100),
                'favorite_count': np.random.randint(5, 500, 100),
                'init_plays': np.random.randint(0, 1000, 100),
                'language': np.random.randint(1, 10, 100),
                'popularity_score': np.random.uniform(100, 50000, 100)
            })
            print("⚠️ 使用示例数据生成图表")

        # 1. 柱状图 - 用户行为类型分布
        plt.figure(figsize=(10, 6))
        action_counts = [
            user_df['play_count'].sum(),
            user_df['download_count'].sum(),
            user_df['favorite_count'].sum()
        ]
        action_labels = ['播放', '下载', '收藏']

        bars = plt.bar(action_labels, action_counts, color=['#ff9999', '#66b3ff', '#99ff99'])
        plt.title('用户行为类型分布', fontproperties=self.font_prop, fontsize=16, fontweight='bold')
        plt.ylabel('次数', fontproperties=self.font_prop, fontsize=12)
        plt.xlabel('行为类型', fontproperties=self.font_prop, fontsize=12)

        # 添加数值标签
        for bar, count in zip(bars, action_counts):
            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + bar.get_height()*0.01,
                    f'{count:,}', ha='center', va='bottom', fontproperties=self.font_prop)

        plt.tight_layout()
        plt.savefig(f"{self.charts_path}/01_用户行为类型分布_柱状图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 散点图 - 歌曲播放vs下载关系
        plt.figure(figsize=(10, 8))
        scatter = plt.scatter(songs_df['play_count'], songs_df['download_count'],
                            c=songs_df['popularity_score'], cmap='viridis', alpha=0.6, s=50)
        plt.xlabel('播放次数', fontproperties=self.font_prop, fontsize=12)
        plt.ylabel('下载次数', fontproperties=self.font_prop, fontsize=12)
        plt.title('歌曲播放次数与下载次数关系分析', fontproperties=self.font_prop, fontsize=16, fontweight='bold')

        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('热度分数', fontproperties=self.font_prop, fontsize=12)

        plt.tight_layout()
        plt.savefig(f"{self.charts_path}/02_歌曲播放下载关系_散点图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 3. 直方图 - 用户活跃度分布
        plt.figure(figsize=(10, 6))
        plt.hist(user_df['total_actions'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.xlabel('总行为数', fontproperties=self.font_prop, fontsize=12)
        plt.ylabel('用户数量', fontproperties=self.font_prop, fontsize=12)
        plt.title('用户活跃度分布直方图', fontproperties=self.font_prop, fontsize=16, fontweight='bold')

        # 添加统计信息
        mean_actions = user_df['total_actions'].mean()
        plt.axvline(mean_actions, color='red', linestyle='--',
                   label=f'平均值: {mean_actions:.1f}')
        plt.legend(prop=self.font_prop)

        plt.tight_layout()
        plt.savefig(f"{self.charts_path}/03_用户活跃度分布_直方图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 4. 饼图 - 语言分布
        plt.figure(figsize=(10, 8))
        language_counts = songs_df['language'].value_counts().head(6)

        wedges, texts, autotexts = plt.pie(language_counts.values,
                                          labels=[f'语言{i}' for i in language_counts.index],
                                          autopct='%1.1f%%', startangle=90,
                                          colors=['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#99ffcc'])

        plt.title('歌曲语言类型分布', fontproperties=self.font_prop, fontsize=16, fontweight='bold')

        # 设置字体
        for text in texts:
            text.set_fontproperties(self.font_prop)
        for autotext in autotexts:
            autotext.set_fontproperties(self.font_prop)

        plt.tight_layout()
        plt.savefig(f"{self.charts_path}/04_歌曲语言分布_饼图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 5. 折线图 - Top艺人热度趋势
        plt.figure(figsize=(12, 6))
        top_artists = songs_df.groupby('artist_id')['popularity_score'].sum().nlargest(15)

        plt.plot(range(len(top_artists)), top_artists.values, marker='o', linewidth=2, markersize=8)
        plt.xlabel('艺人排名', fontproperties=self.font_prop, fontsize=12)
        plt.ylabel('热度分数', fontproperties=self.font_prop, fontsize=12)
        plt.title('Top 15 艺人热度排行趋势', fontproperties=self.font_prop, fontsize=16, fontweight='bold')
        plt.grid(True, alpha=0.3)

        # 设置x轴标签
        plt.xticks(range(len(top_artists)), [f'第{i+1}名' for i in range(len(top_artists))],
                  rotation=45, fontproperties=self.font_prop)

        plt.tight_layout()
        plt.savefig(f"{self.charts_path}/05_艺人热度趋势_折线图.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 6. 热力图 - 用户行为相关性
        plt.figure(figsize=(10, 8))
        correlation_data = user_df[['play_count', 'download_count', 'favorite_count', 'unique_songs']].corr()

        im = plt.imshow(correlation_data, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
        plt.xticks(range(len(correlation_data.columns)), ['播放次数', '下载次数', '收藏次数', '独立歌曲数'],
                  fontproperties=self.font_prop, rotation=45)
        plt.yticks(range(len(correlation_data.columns)), ['播放次数', '下载次数', '收藏次数', '独立歌曲数'],
                  fontproperties=self.font_prop)
        plt.title('用户行为相关性热力图', fontproperties=self.font_prop, fontsize=16, fontweight='bold')

        # 添加数值标签
        for i in range(len(correlation_data.columns)):
            for j in range(len(correlation_data.columns)):
                plt.text(j, i, f'{correlation_data.iloc[i, j]:.2f}',
                        ha='center', va='center', fontproperties=self.font_prop)

        # 添加颜色条
        cbar = plt.colorbar(im)
        cbar.set_label('相关系数', fontproperties=self.font_prop, fontsize=12)

        plt.tight_layout()
        plt.savefig(f"{self.charts_path}/06_用户行为相关性_热力图.png", dpi=300, bbox_inches='tight')
        plt.close()

        print("✅ 可视化图表创建完成 - 6个独立图表文件")

    def run_analysis(self):
        """运行完整分析流程"""
        print("🎵 音乐平台大数据分析项目启动")
        print("="*60)

        try:
            # 1. 初始化Spark
            self.initialize_spark()

            # 2. 数据加载和清洗
            user_actions, songs = self.load_and_clean_data()

            # 3. RDD操作
            user_stats, top_songs = self.rdd_operations(user_actions, songs)

            # 4. SQL查询
            sql_results = self.sql_queries(user_actions, songs)

            # 5. 实时流处理
            streaming_context = self.streaming_analysis()

            # 6. 数据可视化
            self.create_visualizations(user_stats, top_songs, sql_results)

            # 保存结果
            results = {
                'user_stats_count': len(user_stats),
                'top_songs_count': len(top_songs),
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            print("\n" + "="*60)
            print("🎉 分析完成!")
            print(f"📊 用户统计: {results['user_stats_count']} 个用户")
            print(f"🎵 热门歌曲: {results['top_songs_count']} 首")
            print(f"📈 图表保存: {self.charts_path}")
            print("="*60)

            return True

        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            return False
        finally:
            if self.spark:
                self.spark.stop()

if __name__ == "__main__":
    analyzer = MusicAnalyzer()
    analyzer.run_analysis()
