数据预处理：使用 Spark Core 和 Spark SQL 完成数据清洗（如处理缺失值、异常值）、转换（如格式转换、特征提取）和集成，确保数据质量符合分析要求。
数据分析：应用 Spark Core 进行 RDD 操作，实现至少 2 个复杂计算（如分组聚合、连接、排序等）。
数据分析：使用 Spark SQL 完成至少 3 个 SQL 查询，包括复杂查询（如多表连接、窗口函数）。
数据分析：利用 Spark Streaming 或 Structured Streaming 实现实时数据流处理，设计至少 2 个实时分析场景（如实时统计、异常检测）。
数据可视任：针对分析结果生成至少 5 种不同类型的可视化图表（如折线图、柱状图、散点图、热力图、地图等）。
数据可视任：可视化应具有良好的可读性和交互性，能够有效展示数据特征和分析结论。

检查一下 没有实现的补全并且删除无用代码，最后执行一下必须要执行成功。
所有画图都要引用这个字体 是所有画图：/root/job20/SimHei.ttf