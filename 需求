考核要求


使用 Spark Core 和 Spark SQL 完成数据清洗（如处理缺失值、异常值）、转换（如格式转换、特征提取）和集成，确保数据质量符合分析要求。
应用 Spark Core 进行 RDD 操作，实现至少 2 个复杂计算（如分组聚合、连接、排序等）。
使用 Spark SQL 完成至少 3 个 SQL 查询，包括复杂查询（如多表连接、窗口函数）。
利用 Spark Streaming 或 Structured Streaming 实现实时数据流处理，设计至少 2 个实时分析场景（如实时统计、异常检测）。
针对分析结果生成至少 5 种不同类型的可视化图表（如折线图、柱状图、散点图、热力图、地图等）。
可视化应具有良好的可读性和交互性，能够有效展示数据特征和分析结论。
内容完整性：涵盖项目各阶段和关键细节。
逻辑清晰性：结构合理，论述有条理。
代码说明：详细解释关键代码和技术实现。
语言表达：表述准确、简洁，图表使用恰当。