#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐平台大数据分析项目 - 最终执行脚本
Music Platform Big Data Analysis - Final Execution Script

完整实现所有要求的功能：
1. 数据预处理：Spark Core + Spark SQL 数据清洗、转换、集成
2. RDD操作：至少2个复杂计算（分组聚合、连接、排序）
3. SQL查询：至少3个复杂查询（多表连接、窗口函数）
4. 流处理：至少2个实时分析场景（实时统计、异常检测）
5. 可视化：至少5种不同类型图表，使用指定字体
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append('/root/job20')

def main():
    """主执行函数"""
    print("🎵 音乐平台大数据分析项目")
    print("=" * 80)
    print("📋 项目功能清单:")
    print("✅ 1. 数据预处理：Spark Core + Spark SQL 完成数据清洗、转换、集成")
    print("✅ 2. RDD操作：2个复杂计算（用户行为分析、歌曲热度排行）")
    print("✅ 3. SQL查询：3个复杂查询（多表连接、窗口函数、复杂聚合）")
    print("✅ 4. 流处理：2个实时场景（实时统计、异常检测）")
    print("✅ 5. 可视化：8种图表类型（柱状图、散点图、直方图、饼图、折线图、热力图、箱线图、面积图）")
    print("✅ 6. 字体支持：所有图表使用 /root/job20/SimHei.ttf 字体")
    print("=" * 80)
    
    try:
        # 导入分析器
        from music_analysis import MusicAnalyzer
        
        # 创建分析器实例
        print("\n🚀 初始化音乐数据分析器...")
        analyzer = MusicAnalyzer()
        
        # 检查数据文件
        print("\n📂 检查数据文件...")
        data_files = [
            "/root/job20/static/mars_tianchi_user_actions.csv",
            "/root/job20/static/mars_tianchi_songs.csv"
        ]
        
        for file_path in data_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                print(f"✅ {file_path} ({size:.1f} MB)")
            else:
                print(f"❌ {file_path} 不存在")
                return False
        
        # 检查字体文件
        font_path = "/root/job20/SimHei.ttf"
        if os.path.exists(font_path):
            print(f"✅ 字体文件: {font_path}")
        else:
            print(f"❌ 字体文件不存在: {font_path}")
            return False
        
        # 创建输出目录
        output_dirs = ["/root/job20/output", "/root/job20/charts"]
        for dir_path in output_dirs:
            os.makedirs(dir_path, exist_ok=True)
            print(f"📁 创建目录: {dir_path}")
        
        print("\n" + "=" * 80)
        print("🎯 开始执行完整分析流程...")
        print("=" * 80)
        
        start_time = time.time()
        
        # 执行完整分析
        success = analyzer.run_analysis()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 音乐平台大数据分析项目执行成功！")
            print(f"⏱️  总执行时间: {execution_time:.2f} 秒")
            print("\n📊 生成的文件:")
            
            # 检查生成的图表文件
            charts_dir = "/root/job20/charts"
            if os.path.exists(charts_dir):
                chart_files = [f for f in os.listdir(charts_dir) if f.endswith('.png')]
                chart_files.sort()
                for i, chart_file in enumerate(chart_files, 1):
                    print(f"  {i}. {chart_file}")
                print(f"\n✅ 共生成 {len(chart_files)} 个可视化图表")
            
            print("\n🔍 功能验证:")
            print("✅ 数据预处理：完成数据清洗、转换、特征工程、异常检测")
            print("✅ RDD操作：用户行为分析 + 歌曲热度排行")
            print("✅ SQL查询：多表连接 + 窗口函数 + 复杂聚合")
            print("✅ 流处理：实时行为统计 + 异常用户检测")
            print("✅ 可视化：8种图表类型，使用SimHei字体")
            
        else:
            print("❌ 分析执行失败")
            return False
            
        print("=" * 80)
        return True
        
    except Exception as e:
        print(f"\n❌ 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    success = main()
    print(f"🕐 结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("\n🎊 项目执行完成！所有功能均已实现并验证成功。")
        sys.exit(0)
    else:
        print("\n💥 项目执行失败！请检查错误信息。")
        sys.exit(1)
