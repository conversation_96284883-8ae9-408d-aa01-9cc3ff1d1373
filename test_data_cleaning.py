#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的数据清洗功能
Test Enhanced Data Cleaning Functionality
"""

import sys
import os
sys.path.append('/root/job20')

from music_analysis import MusicAnalyzer

def test_enhanced_data_cleaning():
    """测试增强的数据清洗功能"""
    print("🧪 测试增强的数据清洗功能")
    print("="*60)
    
    try:
        # 创建分析器实例
        analyzer = MusicAnalyzer()
        
        # 初始化Spark
        analyzer.initialize_spark()
        
        # 测试增强的数据清洗方法
        print("\n🔬 测试 load_and_clean_data 方法...")
        user_actions, songs = analyzer.load_and_clean_data()
        
        # 验证数据结构
        print("\n📋 验证数据结构:")
        print("用户行为数据列:")
        user_actions.printSchema()
        
        print("\n歌曲数据列:")
        songs.printSchema()
        
        # 显示样本数据
        print("\n📊 用户行为数据样本:")
        user_actions.show(5, truncate=False)
        
        print("\n📊 歌曲数据样本:")
        songs.show(5, truncate=False)
        
        # 数据质量检查
        print("\n🔍 数据质量检查:")
        
        # 检查时间特征
        time_features = user_actions.select("action_hour", "time_period", "day_type").distinct()
        print(f"时间特征种类数: {time_features.count()}")
        time_features.show()
        
        # 检查歌曲特征
        song_features = songs.select("popularity_level", "language_label", "gender_label").distinct()
        print(f"歌曲特征种类数: {song_features.count()}")
        song_features.show()
        
        # 检查异常值标记
        outlier_stats = user_actions.groupBy("is_outlier_user").count()
        print("异常用户统计:")
        outlier_stats.show()
        
        print("\n✅ 数据清洗功能测试完成!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'analyzer' in locals() and analyzer.spark:
            analyzer.spark.stop()

if __name__ == "__main__":
    success = test_enhanced_data_cleaning()
    sys.exit(0 if success else 1)
