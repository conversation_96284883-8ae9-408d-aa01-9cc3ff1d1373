# 音乐平台大数据分析项目

## 🚀 项目概述

基于Apache Spark的音乐平台用户行为数据分析项目，采用企业级**MySQL数据库模式**。

## 🔄 数据流程架构

```
CSV文件 → MySQL数据库 → Spark读取MySQL → 数据处理 → 分析结果 → 可视化
```

## 核心功能

### 1. 数据清洗
- 处理缺失值和异常值
- 数据类型转换和格式标准化
- 时间戳转换和特征提取

### 2. RDD操作（2个复杂计算）
- **用户行为分析**: 分组聚合用户行为统计
- **歌曲热度排行**: 多RDD连接计算热度分数

### 3. SQL查询（3个复杂查询）
- **多表连接**: 用户行为与歌曲信息关联分析
- **窗口函数**: 用户行为趋势分析（移动平均、增长率）
- **复杂聚合**: 艺人热度排行（ROW_NUMBER窗口函数）

### 4. 实时流处理（2个场景）
- **实时统计**: 用户行为实时计数
- **异常检测**: 高频用户行为监控

### 5. 数据可视化（5种图表）
- 柱状图：用户行为类型分布
- 散点图：歌曲播放vs下载关系
- 直方图：用户活跃度分布
- 饼图：歌曲语言分布
- 折线图：Top艺人热度趋势
- 热力图：用户行为相关性

## 数据源

### 用户行为表 (mars_tianchi_user_actions.csv)
| 列名 | 类型 | 说明 |
|------|------|------|
| user_id | String | 用户唯一标识 |
| song_id | String | 歌曲唯一标识 |
| gmt_create | String | 播放时间(unix时间戳) |
| action_type | String | 行为类型(1:播放,2:下载,3:收藏) |
| ds | String | 记录收集日期 |

### 歌曲艺人表 (mars_tianchi_songs.csv)
| 列名 | 类型 | 说明 |
|------|------|------|
| song_id | String | 歌曲唯一标识 |
| artist_id | String | 艺人唯一标识 |
| publish_time | String | 歌曲发行时间 |
| song_init_plays | String | 歌曲初始播放数 |
| language | String | 语言类型编码 |
| gender | String | 性别编码 |

## 技术栈

- **Apache Spark 3.5.0**: 分布式计算引擎
- **Python 3.8+**: 主要开发语言
- **Matplotlib**: 数据可视化（使用SimHei字体）
- **Pandas**: 数据处理

## 项目结构

```
/root/job20/
├── music_analysis.py          # 核心分析模块
├── run_analysis.py           # 项目启动器
├── SimHei.ttf               # 中文字体文件
├── static/                  # 数据文件目录
│   ├── mars_tianchi_user_actions.csv
│   └── mars_tianchi_songs.csv
├── output/                  # 分析结果输出
└── charts/                  # 可视化图表输出
```

## 🚀 运行方式

```bash
# 1. 配置MySQL JDBC驱动
chmod +x setup_mysql_driver.sh
./setup_mysql_driver.sh

# 2. 启动MySQL服务并配置用户
# 确保MySQL用户名: root, 密码: 123456

# 3. 运行分析程序
python run_analysis.py
```

## 环境要求

- Python 3.8+
- Apache Spark 3.5.0
- Java 8/11
- 4GB+ RAM
- SimHei.ttf 中文字体文件

## 输出结果

- 数据清洗报告
- RDD操作统计结果
- SQL查询分析结果
- 实时流处理配置
- 可视化图表文件
- 完整分析报告

## 特色功能

- **中文字体支持**: 所有图表使用SimHei字体显示中文
- **完整错误处理**: 健壮的异常处理机制
- **模块化设计**: 清晰的代码结构和接口
- **详细注释**: 关键代码逻辑详细说明
