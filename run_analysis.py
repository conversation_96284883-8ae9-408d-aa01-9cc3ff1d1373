#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐平台大数据分析项目启动器
"""

import os
import sys
import time
from init_mysql import MySQLInitializer
from music_analysis import MusicAnalyzer

def main():
    """主程序入口"""
    print("🚀 音乐平台大数据分析项目")
    print("=" * 70)
    print("📊 数据流程: CSV → MySQL → Spark读取MySQL → 分析处理")
    print("=" * 70)

    # 1. 数据库初始化
    print("\n📊 第一步: 初始化MySQL数据库...")
    print("-" * 50)

    initializer = MySQLInitializer()
    if not initializer.initialize_all():
        print("❌ MySQL数据库初始化失败")
        return False

    print("\n⏳ 等待3秒，准备开始Spark分析...")
    time.sleep(3)

    # 2. Spark分析
    print("\n⚡ 第二步: 启动Spark分析...")
    print("-" * 50)

    analyzer = MusicAnalyzer()
    if not analyzer.run_analysis():
        print("❌ Spark分析失败")
        return False

    print("\n🎯 项目执行成功！")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行异常: {e}")
        sys.exit(1)
